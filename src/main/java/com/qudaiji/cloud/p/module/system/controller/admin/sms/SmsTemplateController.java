package com.qudaiji.cloud.p.module.system.controller.admin.sms;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.template.SmsTemplatePageReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.template.SmsTemplateRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.template.SmsTemplateSaveReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.template.SmsTemplateSendReqVO;
import com.qudaiji.cloud.p.module.system.entity.SmsTemplateDO;
import com.qudaiji.cloud.p.module.system.service.SmsSendService;
import com.qudaiji.cloud.p.module.system.service.SmsTemplateService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 短信模板
 *
 * <AUTHOR>
 * @date 2025/6/12 16:03
 **/
@RestController
@RequestMapping("/system/sms-template")
public class SmsTemplateController {

    @Resource
    private SmsTemplateService smsTemplateService;
    @Resource
    private SmsSendService smsSendService;

    /**
     * 创建短信模板
     *
     * <AUTHOR>
     * @date 2025/6/12 16:03
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:sms-template:create')")
    public CommonResult<Long> createSmsTemplate(@Valid @RequestBody SmsTemplateSaveReqVO createReqVO) {
        return success(smsTemplateService.createSmsTemplate(createReqVO));
    }

    /**
     * 更新短信模板
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:sms-template:update')")
    public CommonResult<Boolean> updateSmsTemplate(@Valid @RequestBody SmsTemplateSaveReqVO updateReqVO) {
        smsTemplateService.updateSmsTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除短信模板
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:sms-template:delete')")
    public CommonResult<Boolean> deleteSmsTemplate(@RequestParam("id") Long id) {
        smsTemplateService.deleteSmsTemplate(id);
        return success(true);
    }

    /**
     * 获得短信模板
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:sms-template:query')")
    public CommonResult<SmsTemplateRespVO> getSmsTemplate(@RequestParam("id") Long id) {
        SmsTemplateDO template = smsTemplateService.getSmsTemplate(id);
        return success(BeanUtils.toBean(template, SmsTemplateRespVO.class));
    }

    /**
     * 获得短信模板分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:sms-template:query')")
    public CommonResult<PageResult<SmsTemplateRespVO>> getSmsTemplatePage(@Valid SmsTemplatePageReqVO pageVO) {
        PageResult<SmsTemplateDO> pageResult = smsTemplateService.getSmsTemplatePage(pageVO);
        return success(BeanUtils.toBean(pageResult, SmsTemplateRespVO.class));
    }

    /**
     * 导出短信模板 Excel
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @GetMapping("/export-excel")
    @PreAuthorize("@ss.hasPermission('system:sms-template:export')")
    public void exportSmsTemplateExcel(@Valid SmsTemplatePageReqVO exportReqVO,
                                       HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SmsTemplateDO> list = smsTemplateService.getSmsTemplatePage(exportReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "短信模板.xls", "数据", SmsTemplateRespVO.class,
                BeanUtils.toBean(list, SmsTemplateRespVO.class));
    }

    /**
     * 发送短信
     *
     * <AUTHOR>
     * @date 2025/6/12 16:04
     **/
    @PostMapping("/send-sms")
    @PreAuthorize("@ss.hasPermission('system:sms-template:send-sms')")
    public CommonResult<Long> sendSms(@Valid @RequestBody SmsTemplateSendReqVO sendReqVO) {
        return success(smsSendService.sendSingleSmsToAdmin(sendReqVO.getMobile(), null,
                sendReqVO.getTemplateCode(), sendReqVO.getTemplateParams()));
    }

}
