package com.qudaiji.cloud.p.module.system.controller.admin.dict.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import com.qudaiji.cloud.p.common.constants.enums.SystemDictTypeConstants;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 字典类型信息 Response VO
 */
@Data
@ExcelIgnoreUnannotated
public class DictTypeResponse {

    /**
     * 字典类型编号
     * 例如：1024
     */
    @ExcelProperty("字典主键")
    private Long id;

    /**
     * 字典名称
     * 例如：性别
     */
    @ExcelProperty("字典名称")
    private String name;

    /**
     * 字典类型
     * 例如：sys_common_sex
     */
    @ExcelProperty("字典类型")
    private String type;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     * 例如：1
     */
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.COMMON_STATUS)
    private Integer status;

    /**
     * 备注
     * 例如：快乐的备注
     */
    private String remark;

    /**
     * 创建时间
     * 例如：时间戳格式
     */
    private LocalDateTime createTime;

}
