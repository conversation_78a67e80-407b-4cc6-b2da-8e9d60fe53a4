package com.qudaiji.cloud.p.module.system.controller.admin.dict;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.request.DictDataPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.response.DictDataResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.request.DictDataSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.response.DictDataSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.DictDataDO;
import com.qudaiji.cloud.p.module.system.service.DictDataService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 字典数据
 *
 * <AUTHOR>
 * @date 2025/6/12 16:13
 **/
@RestController
@RequestMapping("/system/dict-data")
@Validated
public class DictDataController {

    @Resource
    private DictDataService dictDataService;

    /**
     * 新增字典数据
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:dict:create')")
    public CommonResult<Long> createDictData(@Valid @RequestBody DictDataSaveRequest createReqVO) {
        Long dictDataId = dictDataService.createDictData(createReqVO);
        return success(dictDataId);
    }

    /**
     * 修改字典数据
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:dict:update')")
    public CommonResult<Boolean> updateDictData(@Valid @RequestBody DictDataSaveRequest updateReqVO) {
        dictDataService.updateDictData(updateReqVO);
        return success(true);
    }

    /**
     * 删除字典数据
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:dict:delete')")
    public CommonResult<Boolean> deleteDictData(Long id) {
        dictDataService.deleteDictData(id);
        return success(true);
    }

    /**
     * 获得全部字典数据列表
     * 一般用于管理后台缓存字典数据在本地
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @GetMapping(value = {"/list-all-simple", "simple-list"})
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictDataSimpleResponse>> getSimpleDictDataList() {
        List<DictDataDO> list = dictDataService.getDictDataList(
                CommonStatusEnum.ENABLE.getStatus(), null);
        return success(BeanUtils.toBean(list, DictDataSimpleResponse.class));
    }

    /**
     * 获得字典数据分页列表
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<PageResult<DictDataResponse>> getDictTypePage(@Valid DictDataPageRequest pageReqVO) {
        PageResult<DictDataDO> pageResult = dictDataService.getDictDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DictDataResponse.class));
    }

    /**
     * 获得字典数据详情
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @GetMapping(value = "/get")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<DictDataResponse> getDictData(@RequestParam("id") Long id) {
        DictDataDO dictData = dictDataService.getDictData(id);
        return success(BeanUtils.toBean(dictData, DictDataResponse.class));
    }

    /**
     * 导出字典数据
     *
     * <AUTHOR>
     * @date 2025/6/12 16:13
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:dict:export')")
    public void export(HttpServletResponse response, @Valid DictDataPageRequest exportReqVO) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DictDataDO> list = dictDataService.getDictDataPage(exportReqVO).getList();
        // 输出
        ExcelUtils.write(response, "字典数据.xls", "数据", DictDataResponse.class,
                BeanUtils.toBean(list, DictDataResponse.class));
    }

}
