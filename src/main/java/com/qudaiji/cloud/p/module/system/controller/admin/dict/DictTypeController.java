package com.qudaiji.cloud.p.module.system.controller.admin.dict;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.request.DictTypePageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.response.DictTypeResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.request.DictTypeSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dict.response.DictTypeSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.DictTypeDO;
import com.qudaiji.cloud.p.module.system.service.DictTypeService;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 字典类型
 *
 * <AUTHOR>
 * @date 2025/6/12 15:46
 **/
@RestController
@RequestMapping("/system/dict-type")
@Validated
public class DictTypeController {

    @Resource
    private DictTypeService dictTypeService;

    /**
     * 创建字典类型
     *
     * <AUTHOR>
     * @date 2025/6/12 15:46
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:dict:create')")
    public CommonResult<Long> createDictType(@Valid @RequestBody DictTypeSaveRequest createReqVO) {
        Long dictTypeId = dictTypeService.createDictType(createReqVO);
        return success(dictTypeId);
    }

    /**
     * 修改字典类型
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:dict:update')")
    public CommonResult<Boolean> updateDictType(@Valid @RequestBody DictTypeSaveRequest updateReqVO) {
        dictTypeService.updateDictType(updateReqVO);
        return success(true);
    }

    /**
     * 删除字典类型
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:dict:delete')")
    public CommonResult<Boolean> deleteDictType(Long id) {
        dictTypeService.deleteDictType(id);
        return success(true);
    }

    /**
     * 获得字典类型的分页列表
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<PageResult<DictTypeResponse>> pageDictTypes(@Valid DictTypePageRequest pageReqVO) {
        PageResult<DictTypeDO> pageResult = dictTypeService.getDictTypePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DictTypeResponse.class));
    }

    /**
     * 查询字典类型详细
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @GetMapping(value = "/get")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public CommonResult<DictTypeResponse> getDictType(@RequestParam("id") Long id) {
        DictTypeDO dictType = dictTypeService.getDictType(id);
        return success(BeanUtils.toBean(dictType, DictTypeResponse.class));
    }

    /**
     * 获得全部字典类型列表
     * 包括开启 + 禁用的字典类型，主要用于前端的下拉选项
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @GetMapping(value = {"/list-all-simple", "simple-list"})
    // 无需添加权限认证，因为前端全局都需要
    public CommonResult<List<DictTypeSimpleResponse>> getSimpleDictTypeList() {
        List<DictTypeDO> list = dictTypeService.getDictTypeList();
        return success(BeanUtils.toBean(list, DictTypeSimpleResponse.class));
    }

    /**
     * 导出数据类型
     *
     * <AUTHOR>
     * @date 2025/6/12 15:47
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:dict:query')")
    public void export(HttpServletResponse response, @Valid DictTypePageRequest exportReqVO) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DictTypeDO> list = dictTypeService.getDictTypePage(exportReqVO).getList();
        // 导出
        ExcelUtils.write(response, "字典类型.xls", "数据", DictTypeResponse.class,
                BeanUtils.toBean(list, DictTypeResponse.class));
    }

}
