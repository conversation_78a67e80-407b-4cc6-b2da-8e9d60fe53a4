package com.qudaiji.cloud.p.module.system.controller.admin.dept.request;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.validation.InEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 管理后台 - 岗位创建/修改 Request VO
 */
@Data
public class PostSaveRequest {

    /**
     * 岗位编号
     * 示例：1024
     */
    private Long id;

    /**
     * 岗位名称
     * 必填：是
     * 示例：小土豆
     */
    @NotBlank(message = "岗位名称不能为空")
    @Size(max = 50, message = "岗位名称长度不能超过 50 个字符")
    private String name;

    /**
     * 岗位编码
     * 必填：是
     * 示例：jqm-p-service
     */
    @NotBlank(message = "岗位编码不能为空")
    @Size(max = 64, message = "岗位编码长度不能超过64个字符")
    private String code;

    /**
     * 显示顺序
     * 必填：是
     * 示例：1024
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    /**
     * 状态
     * 必填：是
     * 示例：1
     */
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    /**
     * 备注
     * 示例：快乐的备注
     */
    private String remark;

}