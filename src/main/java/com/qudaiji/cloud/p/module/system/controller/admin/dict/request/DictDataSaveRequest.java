package com.qudaiji.cloud.p.module.system.controller.admin.dict.vo.data;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.validation.InEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 管理后台 - 字典数据创建/修改 Request VO
 */
@Data
public class DictDataSaveReqVO {

    /**
     * 字典数据编号
     * 例如：1024
     */
    private Long id;

    /**
     * 显示顺序
     * 例如：1024
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    /**
     * 字典标签
     */
    @NotBlank(message = "字典标签不能为空")
    @Size(max = 100, message = "字典标签长度不能超过100个字符")
    private String label;

    /**
     * 字典值
     * 例如：iocoder
     */
    @NotBlank(message = "字典键值不能为空")
    @Size(max = 100, message = "字典键值长度不能超过100个字符")
    private String value;

    /**
     * 字典类型
     * 例如：sys_common_sex
     */
    @NotBlank(message = "字典类型不能为空")
    @Size(max = 100, message = "字典类型长度不能超过100个字符")
    private String dictType;

    /**
     * 状态,见 CommonStatusEnum 枚举
     * 例如：1
     */
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

    /**
     * 颜色类型,default、primary、success、info、warning、danger
     * 例如：default
     */
    private String colorType;

    /**
     * css 样式
     * 例如：btn-visible
     */
    private String cssClass;

    /**
     * 备注
     * 例如：我是一个角色
     */
    private String remark;

}
