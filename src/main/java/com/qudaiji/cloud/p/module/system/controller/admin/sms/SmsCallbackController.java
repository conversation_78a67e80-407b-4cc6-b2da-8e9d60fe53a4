package com.qudaiji.cloud.p.module.system.controller.admin.sms;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.servlet.ServletUtils;
import com.qudaiji.cloud.p.module.system.framework.sms.core.enums.SmsChannelEnum;
import com.qudaiji.cloud.p.module.system.service.SmsSendService;

import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 短信回调
 *
 * <AUTHOR>
 * @date 2025/6/12 15:57
 **/
@RestController
@RequestMapping("/system/sms/callback")
public class SmsCallbackController {

    @Resource
    private SmsSendService smsSendService;

    /**
     * 阿里云短信的回调
     * 参见 https://help.aliyun.com/document_detail/120998.html 文档
     *
     * <AUTHOR>
     * @date 2025/6/12 15:57
     **/
    @PostMapping("/aliyun")
    @PermitAll
    public CommonResult<Boolean> receiveAliyunSmsStatus(HttpServletRequest request) throws Throwable {
        String text = ServletUtils.getBody(request);
        smsSendService.receiveSmsStatus(SmsChannelEnum.ALIYUN.getCode(), text);
        return success(true);
    }

    /**
     * 腾讯云短信的回调
     * 参见 https://cloud.tencent.com/document/product/382/52077 文档
     *
     * <AUTHOR>
     * @date 2025/6/12 15:57
     **/
    @PostMapping("/tencent")
    @PermitAll
    public CommonResult<Boolean> receiveTencentSmsStatus(HttpServletRequest request) throws Throwable {
        String text = ServletUtils.getBody(request);
        smsSendService.receiveSmsStatus(SmsChannelEnum.TENCENT.getCode(), text);
        return success(true);
    }


    /**
     * 华为云短信的回调
     * 参见 https://support.huaweicloud.com/api-msgsms/sms_05_0003.html 文档
     *
     * <AUTHOR>
     * @date 2025/6/12 15:57
     **/
    @PostMapping("/huawei")
    @PermitAll
    public CommonResult<Boolean> receiveHuaweiSmsStatus(@RequestBody String requestBody) throws Throwable {
        smsSendService.receiveSmsStatus(SmsChannelEnum.HUAWEI.getCode(), requestBody);
        return success(true);
    }

    /**
     * 七牛云短信的回调
     * 参见 https://developer.qiniu.com/sms/5910/message-push 文档
     *
     * <AUTHOR>
     * @date 2025/6/12 15:57
     **/
    @PostMapping("/qiniu")
    @PermitAll
    public CommonResult<Boolean> receiveQiniuSmsStatus(@RequestBody String requestBody) throws Throwable {
        smsSendService.receiveSmsStatus(SmsChannelEnum.QINIU.getCode(), requestBody);
        return success(true);
    }

}