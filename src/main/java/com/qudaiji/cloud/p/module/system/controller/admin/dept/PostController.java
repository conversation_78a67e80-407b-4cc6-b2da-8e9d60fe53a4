package com.qudaiji.cloud.p.module.system.controller.admin.dept;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.request.PostPageRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.PostResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.request.PostSaveRequest;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.PostSimpleResponse;
import com.qudaiji.cloud.p.module.system.entity.PostDO;
import com.qudaiji.cloud.p.module.system.service.PostService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 岗位
 *
 * <AUTHOR>
 * @date 2025/6/12 16:18
 **/
@RestController
@RequestMapping("/system/post")
@Validated
public class PostController {

    @Resource
    private PostService postService;

    /**
     * 创建岗位
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:post:create')")
    public CommonResult<Long> createPost(@Valid @RequestBody PostSaveRequest createReqVO) {
        Long postId = postService.createPost(createReqVO);
        return success(postId);
    }

    /**
     * 修改岗位
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:post:update')")
    public CommonResult<Boolean> updatePost(@Valid @RequestBody PostSaveRequest updateReqVO) {
        postService.updatePost(updateReqVO);
        return success(true);
    }

    /**
     * 删除岗位
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:post:delete')")
    public CommonResult<Boolean> deletePost(@RequestParam("id") Long id) {
        postService.deletePost(id);
        return success(true);
    }

    /**
     * 获得岗位信息
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @GetMapping(value = "/get")
    @PreAuthorize("@ss.hasPermission('system:post:query')")
    public CommonResult<PostResponse> getPost(@RequestParam("id") Long id) {
        PostDO post = postService.getPost(id);
        return success(BeanUtils.toBean(post, PostResponse.class));
    }

    /**
     * 获取岗位精简信息列表
     * 只包含被开启的岗位，主要用于前端的下拉选项
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @GetMapping(value = {"/list-all-simple", "simple-list"})
    public CommonResult<List<PostSimpleResponse>> getSimplePostList() {
        // 获得岗位列表，只要开启状态的
        List<PostDO> list = postService.getPostList(null, Collections.singleton(CommonStatusEnum.ENABLE.getStatus()));
        // 排序后，返回给前端
        list.sort(Comparator.comparing(PostDO::getSort));
        return success(BeanUtils.toBean(list, PostSimpleResponse.class));
    }

    /**
     * 获得岗位分页列表
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:post:query')")
    public CommonResult<PageResult<PostResponse>> getPostPage(@Validated PostPageRequest pageReqVO) {
        PageResult<PostDO> pageResult = postService.getPostPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PostResponse.class));
    }

    /**
     * 导出岗位 Excel
     *
     * <AUTHOR>
     * @date 2025/6/12 16:18
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:post:export')")
    public void export(HttpServletResponse response, @Validated PostPageRequest reqVO) throws IOException {
        reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PostDO> list = postService.getPostPage(reqVO).getList();
        // 输出
        ExcelUtils.write(response, "岗位数据.xls", "岗位列表", PostResponse.class,
                BeanUtils.toBean(list, PostResponse.class));
    }

}
