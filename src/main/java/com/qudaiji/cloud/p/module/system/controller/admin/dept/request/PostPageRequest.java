package com.qudaiji.cloud.p.module.system.controller.admin.dept.request;

import com.qudaiji.cloud.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类描述
 * 管理后台 - 岗位分页
 *
 * <AUTHOR>
 * @date 2025/6/12 13:09
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class PostPageRequest extends PageParam {

    /**
     * 岗位编码，模糊匹配
     **/
    private String code;

    /**
     * 岗位名称，模糊匹配
     **/
    private String name;

    /**
     * 展示状态，参见 CommonStatusEnum 枚举类
     **/
    private Integer status;

}
