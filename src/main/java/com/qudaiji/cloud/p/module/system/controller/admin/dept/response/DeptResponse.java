package com.qudaiji.cloud.p.module.system.controller.admin.dept.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 部门信息 Response VO
 */
@Data
public class DeptResponse {

    /**
     * 部门编号
     * 示例：1024
     */
    private Long id;

    /**
     * 部门名称
     * 必填：是
     */
    private String name;

    /**
     * 父部门 ID
     * 示例：1024
     */
    private Long parentId;

    /**
     * 显示顺序
     * 必填：是
     * 示例：1024
     */
    private Integer sort;

    /**
     * 负责人的用户编号
     * 示例：2048
     */
    private Long leaderUserId;

    /**
     * 联系电话
     * 示例：15601691000
     */
    private String phone;

    /**
     * 邮箱
     * 示例：<EMAIL>
     */
    private String email;

    /**
     * 状态,见 CommonStatusEnum 枚举
     * 必填：是
     * 示例：1
     */
    private Integer status;

    /**
     * 创建时间
     * 必填：是
     * 示例：时间戳格式
     */
    private LocalDateTime createTime;

}
