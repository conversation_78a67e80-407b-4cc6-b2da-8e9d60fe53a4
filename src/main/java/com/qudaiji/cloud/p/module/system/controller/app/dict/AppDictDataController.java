package com.qudaiji.cloud.p.module.system.controller.app.dict;

import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.app.dict.vo.AppDictDataRespVO;
import com.qudaiji.cloud.p.module.system.entity.DictDataDO;
import com.qudaiji.cloud.p.module.system.service.DictDataService;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 用户 App - 字典数据
 *
 * <AUTHOR>
 * @date 2025/6/12 16:15
 **/
@RestController
@RequestMapping("/system/dict-data")
@Validated
public class AppDictDataController {

    @Resource
    private DictDataService dictDataService;

    /**
     * 根据字典类型查询字典数据信息
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @GetMapping("/type")
    @PermitAll
    public CommonResult<List<AppDictDataRespVO>> getDictDataListByType(@RequestParam("type") String type) {
        List<DictDataDO> list = dictDataService.getDictDataList(
                CommonStatusEnum.ENABLE.getStatus(), type);
        return success(BeanUtils.toBean(list, AppDictDataRespVO.class));
    }

}
