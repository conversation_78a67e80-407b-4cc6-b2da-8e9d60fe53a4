package com.qudaiji.cloud.p.module.system.framework.datapermission.config;

import com.qudaiji.cloud.p.common.datapermission.core.rule.dept.DeptDataPermissionRuleCustomizer;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的数据权限 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class DataPermissionConfiguration {

    @Bean
    public DeptDataPermissionRuleCustomizer sysDeptDataPermissionRuleCustomizer() {
        return rule -> {
            // dept
            rule.addDeptColumn(SystemUserDO.class);
            rule.addDeptColumn(DeptDO.class, "id");
            // user
            rule.addUserColumn(SystemUserDO.class, "id");
        };
    }

}
