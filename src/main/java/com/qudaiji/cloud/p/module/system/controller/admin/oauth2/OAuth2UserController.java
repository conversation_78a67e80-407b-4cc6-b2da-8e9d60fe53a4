package com.qudaiji.cloud.p.module.system.controller.admin.oauth2;

import cn.hutool.core.collection.CollUtil;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.vo.user.OAuth2UserInfoRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.oauth2.vo.user.OAuth2UserUpdateReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.PostDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import com.qudaiji.cloud.p.module.system.service.DeptService;
import com.qudaiji.cloud.p.module.system.service.PostService;
import com.qudaiji.cloud.p.module.system.service.SystemUserService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 提供给外部应用调用为主
 * <p>
 * 1. 在 getUserInfo 方法上，添加 @PreAuthorize("@ss.hasScope('user.read')") 注解，声明需要满足 scope = user.read
 * 2. 在 updateUserInfo 方法上，添加 @PreAuthorize("@ss.hasScope('user.write')") 注解，声明需要满足 scope = user.write
 *
 * <AUTHOR>
 */

/**
 * 管理后台 - OAuth2.0 用户
 *
 * <AUTHOR>
 * @date 2025/6/12 15:27
 **/
@RestController
@RequestMapping("/system/oauth2/user")
@Validated
@Slf4j
public class OAuth2UserController {

    @Resource
    private SystemUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;

    /**
     * 获得用户基本信息
     *
     * <AUTHOR>
     * @date 2025/6/12 15:27
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasScope('user.read')") //
    public CommonResult<OAuth2UserInfoRespVO> getUserInfo() {
        // 获得用户基本信息
        SystemUserDO user = userService.getUser(getLoginUserId());
        OAuth2UserInfoRespVO resp = BeanUtils.toBean(user, OAuth2UserInfoRespVO.class);
        // 获得部门信息
        if (user.getDeptId() != null) {
            DeptDO dept = deptService.getDept(user.getDeptId());
            resp.setDept(BeanUtils.toBean(dept, OAuth2UserInfoRespVO.Dept.class));
        }
        // 获得岗位信息
        if (CollUtil.isNotEmpty(user.getPostIds())) {
            List<PostDO> posts = postService.getPostList(user.getPostIds());
            resp.setPosts(BeanUtils.toBean(posts, OAuth2UserInfoRespVO.Post.class));
        }
        return success(resp);
    }

    /**
     * 更新用户基本信息
     *
     * <AUTHOR>
     * @date 2025/6/12 15:27
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasScope('user.write')")
    public CommonResult<Boolean> updateUserInfo(@Valid @RequestBody OAuth2UserUpdateReqVO reqVO) {
        // 这里将 UserProfileUpdateReqVO =》UserProfileUpdateReqVO 对象，实现接口的复用。
        // 主要是，AdminUserService 没有自己的 BO 对象，所以复用只能这么做
        userService.updateUserProfile(getLoginUserId(), BeanUtils.toBean(reqVO, UserProfileUpdateReqVO.class));
        return success(true);
    }

}
