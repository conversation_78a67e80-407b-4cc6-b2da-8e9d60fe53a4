package com.qudaiji.cloud.p.module.system.controller.admin.logger.vo.operatelog;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 操作日志 Response VO
 */
@Data
@ExcelIgnoreUnannotated
public class OperateLogRespVO implements VO {

    /**
     * 日志编号
     * 示例值：1024
     */
    @ExcelProperty("日志编号")
    private Long id;

    /**
     * 链路追踪编号
     * 示例值：89aca178-a370-411c-ae02-3f0d672be4ab
     */
    private String traceId;

    /**
     * 用户编号
     * 示例值：1024
     */
    @Trans(type = TransType.SIMPLE, target = SystemUserDO.class, fields = "nickname", ref = "userName")
    private Long userId;

    /**
     * 用户昵称
     * 示例值：芋艿
     */
    @ExcelProperty("操作人")
    private String userName;

    /**
     * 操作模块类型
     * 示例值：订单
     */
    @ExcelProperty("操作模块类型")
    private String type;

    /**
     * 操作名
     * 示例值：创建订单
     */
    @ExcelProperty("操作名")
    private String subType;

    /**
     * 操作模块业务编号
     * 示例值：1
     */
    @ExcelProperty("操作模块业务编号")
    private Long bizId;

    /**
     * 操作明细
     * 示例值：修改编号为 1 的用户信息，将性别从男改成女，将姓名从芋道改成源码。
     */
    private String action;

    /**
     * 拓展字段
     * 示例值：{'orderId': 1}
     */
    private String extra;

    /**
     * 请求方法名
     * 示例值：GET
     */
    @NotEmpty(message = "请求方法名不能为空")
    private String requestMethod;

    /**
     * 请求地址
     * 示例值：/xxx/yyy
     */
    private String requestUrl;

    /**
     * 用户 IP
     * 示例值：127.0.0.1
     */
    private String userIp;

    /**
     * 浏览器 UserAgent
     * 示例值：Mozilla/5.0
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
