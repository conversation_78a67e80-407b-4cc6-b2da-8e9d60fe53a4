package com.qudaiji.cloud.p.module.system.controller.admin.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.framework.common.enums.CommonStatusEnum;
import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.p.common.constants.enums.logger.LoginLogTypeEnum;
import com.qudaiji.cloud.p.common.security.config.SecurityProperties;
import com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.auth.vo.*;
import com.qudaiji.cloud.p.module.system.convert.auth.AuthConvert;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import com.qudaiji.cloud.p.module.system.entity.MenuDO;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.service.*;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.framework.common.util.collection.CollectionUtils.convertSet;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 认证
 *
 * <AUTHOR>
 * @date 2025/6/12 15:16
 **/
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private SystemUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SocialClientService socialClientService;

    @Resource
    private SecurityProperties securityProperties;

    /**
     * 使用账号密码登录
     *
     * <AUTHOR>
     * @date 2025/6/12 15:16
     **/
    @PostMapping("/login")
    @PermitAll
    public CommonResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    /**
     * 登出系统
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @PostMapping("/logout")
    @PermitAll
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    /**
     * 刷新令牌
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @PostMapping("/refresh-token")
    @PermitAll
    public CommonResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    /**
     * 获取登录用户的权限信息
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @GetMapping("/get-permission-info")
    public CommonResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 1.1 获得用户信息
        SystemUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return success(null);
        }

        // 1.2 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
        if (CollUtil.isEmpty(roleIds)) {
            return success(AuthConvert.INSTANCE.convert(user, Collections.emptyList(), Collections.emptyList()));
        }
        List<RoleDO> roles = roleService.getRoleList(roleIds);
        roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色

        // 1.3 获得菜单列表
        Set<Long> menuIds = permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId));
        List<MenuDO> menuList = menuService.getMenuList(menuIds);
        menuList = menuService.filterDisableMenus(menuList);

        // 2. 拼接结果返回
        return success(AuthConvert.INSTANCE.convert(user, roles, menuList));
    }

    /**
     * 注册用户
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @PostMapping("/register")
    @PermitAll
    public CommonResult<AuthLoginRespVO> register(@RequestBody @Valid AuthRegisterReqVO registerReqVO) {
        return success(authService.register(registerReqVO));
    }

    // ========== 短信登录相关 ==========

    /**
     * 使用短信验证码登录
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @PostMapping("/sms-login")
    @PermitAll
    public CommonResult<AuthLoginRespVO> smsLogin(@RequestBody @Valid AuthSmsLoginReqVO reqVO) {
        return success(authService.smsLogin(reqVO));
    }

    /**
     * 发送手机验证码
     *
     * <AUTHOR>
     * @date 2025/6/12 15:17
     **/
    @PostMapping("/send-sms-code")
    @PermitAll
    public CommonResult<Boolean> sendLoginSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        authService.sendSmsCode(reqVO);
        return success(true);
    }

    // ========== 社交登录相关 ==========

    /**
     * 社交授权的跳转
     *
     * @param type        社交类型
     * @param redirectUri 回调路径
     * <AUTHOR>
     * @date 2025/6/12 15:18
     **/
    @GetMapping("/social-auth-redirect")
    @PermitAll
    public CommonResult<String> socialLogin(@RequestParam("type") Integer type,
                                            @RequestParam("redirectUri") String redirectUri) {
        return success(socialClientService.getAuthorizeUrl(
                type, UserTypeEnum.ADMIN.getValue(), redirectUri));
    }

    /**
     * 社交快捷登录，使用 code 授权码
     *
     * @param reqVO 登录信息
     * <AUTHOR>
     * @date 2025/6/12 15:18
     **/
    @PostMapping("/social-login")
    @PermitAll
    public CommonResult<AuthLoginRespVO> socialQuickLogin(@RequestBody @Valid AuthSocialLoginReqVO reqVO) {
        return success(authService.socialLogin(reqVO));
    }

}
