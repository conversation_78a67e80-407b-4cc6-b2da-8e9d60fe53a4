package com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile;

import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.DeptSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.PostSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理后台 - 用户个人中心信息 Response VO
 */
@Data
public class UserProfileRespVO {

    /**
     * 用户编号
     * 例如：1
     */
    private Long id;

    /**
     * 用户账号
     * 例如：jqm-p-service
     */
    private String username;

    /**
     * 用户昵称
     * 例如：芋艿
     */
    private String nickname;

    /**
     * 用户邮箱
     * 例如：<EMAIL>
     */
    private String email;

    /**
     * 手机号码
     * 例如：15601691300
     */
    private String mobile;

    /**
     * 用户性别，参见 SexEnum 枚举类
     * 例如：1
     */
    private Integer sex;

    /**
     * 用户头像
     * 例如：https://www.iocoder.cn/xxx.png
     */
    private String avatar;

    /**
     * 最后登录 IP
     * 例如：***********
     */
    private String loginIp;

    /**
     * 最后登录时间
     * 例如：时间戳格式
     */
    private LocalDateTime loginDate;

    /**
     * 创建时间
     * 例如：时间戳格式
     */
    private LocalDateTime createTime;

    /**
     * 所属角色
     */
    private List<RoleSimpleRespVO> roles;
    /**
     * 所在部门
     */
    private DeptSimpleResponse dept;
    /**
     * 所属岗位数组
     */
    private List<PostSimpleResponse> posts;
    /**
     * 社交用户数组
     */
    private List<SocialUser> socialUsers;

    /**
     * 社交用户
     */
    @Data
    public static class SocialUser {

        /**
         * 社交平台的类型，参见 SocialTypeEnum 枚举类
         * 例如：10
         */
        private Integer type;

        /**
         * 社交用户的 openid
         * 例如：IPRmJ0wvBptiPIlGEZiPewGwiEiE
         */
        private String openid;

    }

}
