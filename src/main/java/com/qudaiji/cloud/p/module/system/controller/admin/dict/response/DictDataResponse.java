package com.qudaiji.cloud.p.module.system.controller.admin.dict.vo.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qudaiji.cloud.p.common.excel.excel.core.annotations.DictFormat;
import com.qudaiji.cloud.p.common.excel.excel.core.convert.DictConvert;
import com.qudaiji.cloud.p.common.constants.enums.SystemDictTypeConstants;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理后台 - 字典数据信息 Response VO
 */
@Data
@ExcelIgnoreUnannotated
public class DictDataRespVO {

    /**
     * 字典数据编号
     * 例如：1024
     */
    @ExcelProperty("字典编码")
    private Long id;

    /**
     * 显示顺序
     * 例如：1024
     */
    @ExcelProperty("字典排序")
    private Integer sort;

    /**
     * 字典标签
     */
    @ExcelProperty("字典标签")
    private String label;

    /**
     * 字典值
     * 例如：iocoder
     */
    @ExcelProperty("字典键值")
    private String value;

    /**
     * 字典类型
     * 例如：sys_common_sex
     */
    @ExcelProperty("字典类型")
    private String dictType;

    /**
     * 状态,见 CommonStatusEnum 枚举
     * 例如：1
     */
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(SystemDictTypeConstants.COMMON_STATUS)
    private Integer status;

    /**
     * 颜色类型,default、primary、success、info、warning、danger
     * 例如：default
     */
    private String colorType;

    /**
     * css 样式
     * 例如：btn-visible
     */
    private String cssClass;

    /**
     * 备注
     * 例如：我是一个角色
     */
    private String remark;

    /**
     * 创建时间
     * 例如：时间戳格式
     */
    private LocalDateTime createTime;

}
