package com.qudaiji.cloud.p.module.system.controller.admin.sms;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.channel.SmsChannelPageReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.channel.SmsChannelRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.channel.SmsChannelSaveReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.sms.vo.channel.SmsChannelSimpleRespVO;
import com.qudaiji.cloud.p.module.system.entity.SmsChannelDO;
import com.qudaiji.cloud.p.module.system.service.SmsChannelService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 短信渠道
 *
 * <AUTHOR>
 * @date 2025/6/12 16:08
 **/
@RestController
@RequestMapping("system/sms-channel")
public class SmsChannelController {

    @Resource
    private SmsChannelService smsChannelService;

    /**
     * 创建短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:create')")
    public CommonResult<Long> createSmsChannel(@Valid @RequestBody SmsChannelSaveReqVO createReqVO) {
        return success(smsChannelService.createSmsChannel(createReqVO));
    }

    /**
     * 更新短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:update')")
    public CommonResult<Boolean> updateSmsChannel(@Valid @RequestBody SmsChannelSaveReqVO updateReqVO) {
        smsChannelService.updateSmsChannel(updateReqVO);
        return success(true);
    }

    /**
     * 删除短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:delete')")
    public CommonResult<Boolean> deleteSmsChannel(@RequestParam("id") Long id) {
        smsChannelService.deleteSmsChannel(id);
        return success(true);
    }

    /**
     * 获得短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:query')")
    public CommonResult<SmsChannelRespVO> getSmsChannel(@RequestParam("id") Long id) {
        SmsChannelDO channel = smsChannelService.getSmsChannel(id);
        return success(BeanUtils.toBean(channel, SmsChannelRespVO.class));
    }

    /**
     * 获得短信渠道分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:sms-channel:query')")
    public CommonResult<PageResult<SmsChannelRespVO>> getSmsChannelPage(@Valid SmsChannelPageReqVO pageVO) {
        PageResult<SmsChannelDO> pageResult = smsChannelService.getSmsChannelPage(pageVO);
        return success(BeanUtils.toBean(pageResult, SmsChannelRespVO.class));
    }

    /**
     * 获得短信渠道精简列表
     * 包含被禁用的短信渠道
     *
     * <AUTHOR>
     * @date 2025/6/12 16:08
     **/
    @GetMapping({"/list-all-simple", "/simple-list"})
    public CommonResult<List<SmsChannelSimpleRespVO>> getSimpleSmsChannelList() {
        List<SmsChannelDO> list = smsChannelService.getSmsChannelList();
        list.sort(Comparator.comparing(SmsChannelDO::getId));
        return success(BeanUtils.toBean(list, SmsChannelSimpleRespVO.class));
    }

}
