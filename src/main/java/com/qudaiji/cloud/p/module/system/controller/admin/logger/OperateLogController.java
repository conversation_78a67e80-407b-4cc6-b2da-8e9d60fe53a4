package com.qudaiji.cloud.p.module.system.controller.admin.logger;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageParam;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.framework.translate.core.TranslateUtils;
import com.qudaiji.cloud.p.common.excel.excel.core.util.ExcelUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.logger.vo.operatelog.OperateLogRespVO;
import com.qudaiji.cloud.p.module.system.entity.OperateLogDO;
import com.qudaiji.cloud.p.module.system.service.OperateLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 操作日志
 *
 * <AUTHOR>
 * @date 2025/6/12 16:17
 **/
@RestController
@RequestMapping("/system/operate-log")
@Validated
public class OperateLogController {

    @Resource
    private OperateLogService operateLogService;

    /**
     * 查看操作日志分页列表
     *
     * <AUTHOR>
     * @date 2025/6/12 16:17
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:operate-log:query')")
    public CommonResult<PageResult<OperateLogRespVO>> pageOperateLog(@Valid OperateLogPageReqVO pageReqVO) {
        PageResult<OperateLogDO> pageResult = operateLogService.getOperateLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OperateLogRespVO.class));
    }

    /**
     * 导出操作日志
     *
     * <AUTHOR>
     * @date 2025/6/12 16:17
     **/
    @GetMapping("/export")
    @PreAuthorize("@ss.hasPermission('system:operate-log:export')")
    public void exportOperateLog(HttpServletResponse response, @Valid OperateLogPageReqVO exportReqVO) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OperateLogDO> list = operateLogService.getOperateLogPage(exportReqVO).getList();
        ExcelUtils.write(response, "操作日志.xls", "数据列表", OperateLogRespVO.class,
                TranslateUtils.translate(BeanUtils.toBean(list, OperateLogRespVO.class)));
    }

}
