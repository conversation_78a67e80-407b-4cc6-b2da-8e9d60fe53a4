package com.qudaiji.cloud.p.module.system.convert.user;

import com.qudaiji.cloud.framework.common.util.collection.CollectionUtils;
import com.qudaiji.cloud.framework.common.util.collection.MapUtils;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.DeptSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.dept.response.PostSimpleResponse;
import com.qudaiji.cloud.p.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile.UserProfileRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.user.UserRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.user.UserSimpleRespVO;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.PostDO;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.entity.SocialUserDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    default List<UserRespVO> convertList(List<SystemUserDO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> convert(user, deptMap.get(user.getDeptId())));
    }

    default UserRespVO convert(SystemUserDO user, DeptDO dept) {
        UserRespVO userVO = BeanUtils.toBean(user, UserRespVO.class);
        if (dept != null) {
            userVO.setDeptName(dept.getName());
        }
        return userVO;
    }

    default List<UserSimpleRespVO> convertSimpleList(List<SystemUserDO> list, Map<Long, DeptDO> deptMap) {
        return CollectionUtils.convertList(list, user -> {
            UserSimpleRespVO userVO = BeanUtils.toBean(user, UserSimpleRespVO.class);
            MapUtils.findAndThen(deptMap, user.getDeptId(), dept -> userVO.setDeptName(dept.getName()));
            return userVO;
        });
    }

    default UserProfileRespVO convert(SystemUserDO user, List<RoleDO> userRoles,
                                      DeptDO dept, List<PostDO> posts, List<SocialUserDO> socialUsers) {
        UserProfileRespVO userVO = BeanUtils.toBean(user, UserProfileRespVO.class);
        userVO.setRoles(BeanUtils.toBean(userRoles, RoleSimpleRespVO.class));
        userVO.setDept(BeanUtils.toBean(dept, DeptSimpleResponse.class));
        userVO.setPosts(BeanUtils.toBean(posts, PostSimpleResponse.class));
        userVO.setSocialUsers(BeanUtils.toBean(socialUsers, UserProfileRespVO.SocialUser.class));
        return userVO;
    }

}
