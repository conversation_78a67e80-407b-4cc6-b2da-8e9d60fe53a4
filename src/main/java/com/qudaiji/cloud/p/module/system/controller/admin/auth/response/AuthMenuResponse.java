package com.qudaiji.cloud.p.module.system.controller.admin.auth.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 管理后台 - 登录用户的菜单信息 Response VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthMenuRespVO {

    /**
     * 菜单名称
     * 必填：是
     */
    private Long id;

    /**
     * 父菜单 ID
     * 必填：是
     */
    private Long parentId;

    /**
     * 菜单名称
     * 必填：是
     */
    private String name;

    /**
     * 路由地址,仅菜单类型为菜单或者目录时，才需要传
     * 示例：post
     */
    private String path;

    /**
     * 组件路径,仅菜单类型为菜单时，才需要传
     * 示例：system/post/index
     */
    private String component;

    /**
     * 组件名
     * 示例：SystemUser
     */
    private String componentName;

    /**
     * 菜单图标,仅菜单类型为菜单或者目录时，才需要传
     * 示例：/menu/list
     */
    private String icon;

    /**
     * 是否可见
     * 必填：是
     * 示例：false
     */
    private Boolean visible;

    /**
     * 是否缓存
     * 必填：是
     * 示例：false
     */
    private Boolean keepAlive;

    /**
     * 是否总是显示
     * 示例：false
     */
    private Boolean alwaysShow;

    /**
     * 子路由
     */
    private List<AuthMenuRespVO> children;

}
