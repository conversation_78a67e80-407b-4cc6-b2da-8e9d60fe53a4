package com.qudaiji.cloud.p.module.system.controller.admin.user;

import cn.hutool.core.collection.CollUtil;
import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.p.common.datapermission.core.annotation.DataPermission;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile.UserProfileRespVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import com.qudaiji.cloud.p.module.system.convert.user.UserConvert;
import com.qudaiji.cloud.p.module.system.entity.DeptDO;
import com.qudaiji.cloud.p.module.system.entity.PostDO;
import com.qudaiji.cloud.p.module.system.entity.RoleDO;
import com.qudaiji.cloud.p.module.system.entity.SocialUserDO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import com.qudaiji.cloud.p.module.system.service.DeptService;
import com.qudaiji.cloud.p.module.system.service.PostService;
import com.qudaiji.cloud.p.module.system.service.PermissionService;
import com.qudaiji.cloud.p.module.system.service.RoleService;
import com.qudaiji.cloud.p.module.system.service.SocialUserService;
import com.qudaiji.cloud.p.module.system.service.SystemUserService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.qudaiji.cloud.p.common.constants.enums.InfraErrorCodeConstants.FILE_IS_EMPTY;

/**
 * 管理后台 - 用户个人中心
 *
 * <AUTHOR>
 * @date 2025/6/12 16:14
 **/
@RestController
@RequestMapping("/system/user/profile")
@Validated
@Slf4j
public class UserProfileController {

    @Resource
    private SystemUserService userService;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private SocialUserService socialService;

    /**
     * 获得登录用户信息
     *
     * <AUTHOR>
     * @date 2025/6/12 16:14
     **/
    @GetMapping("/get")
    @DataPermission(enable = false) // 关闭数据权限，避免只查看自己时，查询不到部门。
    public CommonResult<UserProfileRespVO> getUserProfile() {
        // 获得用户基本信息
        SystemUserDO user = userService.getUser(getLoginUserId());
        // 获得用户角色
        List<RoleDO> userRoles = roleService.getRoleListFromCache(permissionService.getUserRoleIdListByUserId(user.getId()));
        // 获得部门信息
        DeptDO dept = user.getDeptId() != null ? deptService.getDept(user.getDeptId()) : null;
        // 获得岗位信息
        List<PostDO> posts = CollUtil.isNotEmpty(user.getPostIds()) ? postService.getPostList(user.getPostIds()) : null;
        // 获得社交用户信息
        List<SocialUserDO> socialUsers = socialService.getSocialUserList(user.getId(), UserTypeEnum.ADMIN.getValue());
        return success(UserConvert.INSTANCE.convert(user, userRoles, dept, posts, socialUsers));
    }

    /**
     * 修改用户个人信息
     *
     * <AUTHOR>
     * @date 2025/6/12 16:14
     **/
    @PutMapping("/update")
    public CommonResult<Boolean> updateUserProfile(@Valid @RequestBody UserProfileUpdateReqVO reqVO) {
        userService.updateUserProfile(getLoginUserId(), reqVO);
        return success(true);
    }

    /**
     * 修改用户个人密码
     *
     * <AUTHOR>
     * @date 2025/6/12 16:14
     **/
    @PutMapping("/update-password")
    public CommonResult<Boolean> updateUserProfilePassword(@Valid @RequestBody UserProfileUpdatePasswordReqVO reqVO) {
        userService.updateUserPassword(getLoginUserId(), reqVO);
        return success(true);
    }

    /**
     * 上传用户个人头像
     *
     * <AUTHOR>
     * @date 2025/6/12 16:14
     **/
    @RequestMapping(value = "/update-avatar",
            method = {RequestMethod.POST, RequestMethod.PUT}) // 解决 uni-app 不支持 Put 上传文件的问题
    public CommonResult<String> updateUserAvatar(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = userService.updateUserAvatar(getLoginUserId(), file.getInputStream());
        return success(avatar);
    }

}
