package com.qudaiji.cloud.p.module.system.mapper;

import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.mybatis.core.mapper.BaseMapperX;
import com.qudaiji.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.qudaiji.cloud.p.module.system.controller.admin.user.vo.user.UserPageReqVO;
import com.qudaiji.cloud.p.module.system.entity.SystemUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface SystemUserMapper extends BaseMapperX<SystemUserDO> {

    default SystemUserDO selectByUsername(String username) {
        return selectOne(SystemUserDO::getUsername, username);
    }

    default SystemUserDO selectByEmail(String email) {
        return selectOne(SystemUserDO::getEmail, email);
    }

    default SystemUserDO selectByMobile(String mobile) {
        return selectOne(SystemUserDO::getMobile, mobile);
    }

    default PageResult<SystemUserDO> selectPage(UserPageReqVO reqVO, Collection<Long> deptIds) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SystemUserDO>()
                .likeIfPresent(SystemUserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(SystemUserDO::getMobile, reqVO.getMobile())
                .eqIfPresent(SystemUserDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SystemUserDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent(SystemUserDO::getDeptId, deptIds)
                .orderByDesc(SystemUserDO::getId));
    }

    default List<SystemUserDO> selectListByNickname(String nickname) {
        return selectList(new LambdaQueryWrapperX<SystemUserDO>().like(SystemUserDO::getNickname, nickname));
    }

    default List<SystemUserDO> selectListByStatus(Integer status) {
        return selectList(SystemUserDO::getStatus, status);
    }

    default List<SystemUserDO> selectListByDeptIds(Collection<Long> deptIds) {
        return selectList(SystemUserDO::getDeptId, deptIds);
    }

}
