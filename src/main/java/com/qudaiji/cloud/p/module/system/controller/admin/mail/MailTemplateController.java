package com.qudaiji.cloud.p.module.system.controller.admin.mail;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.p.module.system.controller.admin.mail.vo.template.*;
import com.qudaiji.cloud.p.module.system.entity.MailTemplateDO;
import com.qudaiji.cloud.p.module.system.service.MailSendService;
import com.qudaiji.cloud.p.module.system.service.MailTemplateService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;
import static com.qudaiji.cloud.p.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 邮件模版
 *
 * <AUTHOR>
 * @date 2025/6/12 16:16
 **/
@RestController
@RequestMapping("/system/mail-template")
public class MailTemplateController {

    @Resource
    private MailTemplateService mailTempleService;
    @Resource
    private MailSendService mailSendService;

    /**
     * 创建邮件模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('system:mail-template:create')")
    public CommonResult<Long> createMailTemplate(@Valid @RequestBody MailTemplateSaveReqVO createReqVO) {
        return success(mailTempleService.createMailTemplate(createReqVO));
    }

    /**
     * 修改邮件模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('system:mail-template:update')")
    public CommonResult<Boolean> updateMailTemplate(@Valid @RequestBody MailTemplateSaveReqVO updateReqVO) {
        mailTempleService.updateMailTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除邮件模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:mail-template:delete')")
    public CommonResult<Boolean> deleteMailTemplate(@RequestParam("id") Long id) {
        mailTempleService.deleteMailTemplate(id);
        return success(true);
    }

    /**
     * 获得邮件模版
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('system:mail-template:query')")
    public CommonResult<MailTemplateRespVO> getMailTemplate(@RequestParam("id") Long id) {
        MailTemplateDO template = mailTempleService.getMailTemplate(id);
        return success(BeanUtils.toBean(template, MailTemplateRespVO.class));
    }

    /**
     * 获得邮件模版分页
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('system:mail-template:query')")
    public CommonResult<PageResult<MailTemplateRespVO>> getMailTemplatePage(@Valid MailTemplatePageReqVO pageReqVO) {
        PageResult<MailTemplateDO> pageResult = mailTempleService.getMailTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MailTemplateRespVO.class));
    }

    /**
     * 获得邮件模版精简列表
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @GetMapping({"/list-all-simple", "simple-list"})
    public CommonResult<List<MailTemplateSimpleRespVO>> getSimpleTemplateList() {
        List<MailTemplateDO> list = mailTempleService.getMailTemplateList();
        return success(BeanUtils.toBean(list, MailTemplateSimpleRespVO.class));
    }

    /**
     * 发送邮件
     *
     * <AUTHOR>
     * @date 2025/6/12 16:16
     **/
    @PostMapping("/send-mail")
    @PreAuthorize("@ss.hasPermission('system:mail-template:send-mail')")
    public CommonResult<Long> sendMail(@Valid @RequestBody MailTemplateSendReqVO sendReqVO) {
        return success(mailSendService.sendSingleMailToAdmin(sendReqVO.getMail(), getLoginUserId(),
                sendReqVO.getTemplateCode(), sendReqVO.getTemplateParams()));
    }

}
